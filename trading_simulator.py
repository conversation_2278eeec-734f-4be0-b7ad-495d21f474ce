import numpy as np
import pandas as pd
from typing import List, Tu<PERSON>, Dict, Any
from neural_network import NeuralNetwork

class TradingSimulator:
    """
    Simulates cryptocurrency trading based on neural network signals.
    Includes transaction fees, position management, and performance tracking.
    """
    
    def __init__(self, initial_capital: float = 10000.0, transaction_fee: float = 0.001):
        self.initial_capital = initial_capital
        self.transaction_fee = transaction_fee  # 0.1% fee per trade
        
        # Trading state
        self.capital = initial_capital
        self.position = 0.0  # Amount of cryptocurrency held
        self.position_value = 0.0
        self.total_value = initial_capital
        
        # Trading history
        self.trades = []
        self.portfolio_history = []
        self.signals_history = []
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
    def reset(self):
        """Reset the simulator to initial state."""
        self.capital = self.initial_capital
        self.position = 0.0
        self.position_value = 0.0
        self.total_value = self.initial_capital
        
        self.trades.clear()
        self.portfolio_history.clear()
        self.signals_history.clear()
        
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
    
    def execute_trade(self, signal: int, price: float, timestamp: Any) -> bool:
        """
        Execute a trade based on the neural network signal.
        
        Args:
            signal: 0=Buy, 1=Sell, 2=Hold
            price: Current price
            timestamp: Current timestamp
            
        Returns:
            bool: True if trade was executed, False otherwise
        """
        trade_executed = False
        
        if signal == 0:  # Buy signal
            if self.capital > 0 and self.position == 0:  # Only buy if we have capital and no position
                # Calculate amount to buy (use all available capital)
                gross_amount = self.capital / price
                fee = gross_amount * self.transaction_fee
                net_amount = gross_amount - fee
                
                if net_amount > 0:
                    self.position = net_amount
                    self.capital = 0.0
                    
                    # Record trade
                    trade = {
                        'type': 'BUY',
                        'amount': net_amount,
                        'price': price,
                        'fee': fee * price,
                        'timestamp': timestamp,
                        'capital_before': self.capital + (gross_amount * price),
                        'capital_after': self.capital
                    }
                    self.trades.append(trade)
                    self.total_trades += 1
                    trade_executed = True
        
        elif signal == 1:  # Sell signal
            if self.position > 0:  # Only sell if we have a position
                # Calculate proceeds from sale
                gross_proceeds = self.position * price
                fee = gross_proceeds * self.transaction_fee
                net_proceeds = gross_proceeds - fee
                
                # Record trade
                trade = {
                    'type': 'SELL',
                    'amount': self.position,
                    'price': price,
                    'fee': fee,
                    'timestamp': timestamp,
                    'capital_before': self.capital,
                    'capital_after': self.capital + net_proceeds
                }
                
                # Check if this was a winning or losing trade
                if len(self.trades) > 0:
                    last_buy = None
                    for t in reversed(self.trades):
                        if t['type'] == 'BUY':
                            last_buy = t
                            break
                    
                    if last_buy:
                        profit = net_proceeds - (last_buy['amount'] * last_buy['price'] + last_buy['fee'])
                        if profit > 0:
                            self.winning_trades += 1
                        else:
                            self.losing_trades += 1
                
                self.trades.append(trade)
                self.capital = net_proceeds
                self.position = 0.0
                self.total_trades += 1
                trade_executed = True
        
        # signal == 2 is Hold, no action needed
        
        return trade_executed
    
    def update_portfolio_value(self, price: float, timestamp: Any):
        """Update current portfolio value."""
        self.position_value = self.position * price
        self.total_value = self.capital + self.position_value
        
        portfolio_entry = {
            'timestamp': timestamp,
            'capital': self.capital,
            'position': self.position,
            'position_value': self.position_value,
            'total_value': self.total_value,
            'price': price
        }
        self.portfolio_history.append(portfolio_entry)
    
    def simulate_trading(self, neural_network: NeuralNetwork, features: np.ndarray, 
                        prices: np.ndarray, timestamps: List[Any]) -> Dict[str, Any]:
        """
        Run complete trading simulation.
        
        Args:
            neural_network: Trained neural network
            features: Feature matrix for predictions
            prices: Price array corresponding to features
            timestamps: Timestamp array
            
        Returns:
            Dict containing simulation results
        """
        self.reset()
        
        # Get predictions from neural network
        predictions = neural_network.predict(features)
        
        # Execute trades based on predictions
        for i, (signal, price, timestamp) in enumerate(zip(predictions, prices, timestamps)):
            # Execute trade if signal is not hold
            trade_executed = self.execute_trade(signal, price, timestamp)
            
            # Update portfolio value
            self.update_portfolio_value(price, timestamp)
            
            # Record signal
            signal_entry = {
                'timestamp': timestamp,
                'signal': signal,
                'price': price,
                'trade_executed': trade_executed
            }
            self.signals_history.append(signal_entry)
        
        # Close any remaining position at the end
        if self.position > 0:
            final_price = prices[-1]
            final_timestamp = timestamps[-1]
            self.execute_trade(1, final_price, final_timestamp)  # Force sell
            self.update_portfolio_value(final_price, final_timestamp)
        
        return self.get_simulation_results()
    
    def get_simulation_results(self) -> Dict[str, Any]:
        """Get comprehensive simulation results."""
        if len(self.portfolio_history) == 0:
            return {'error': 'No simulation data available'}
        
        # Calculate performance metrics
        initial_value = self.initial_capital
        final_value = self.total_value
        total_return = (final_value - initial_value) / initial_value
        
        # Calculate returns for Sharpe ratio
        portfolio_values = [entry['total_value'] for entry in self.portfolio_history]
        returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # Sharpe ratio (assuming risk-free rate of 0)
        if len(returns) > 1 and np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252 * 24 * 60)  # Annualized for 1-minute data
        else:
            sharpe_ratio = 0.0
        
        # Maximum drawdown
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (peak - portfolio_values) / peak
        max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0.0
        
        # Win rate
        win_rate = self.winning_trades / max(1, self.winning_trades + self.losing_trades)
        
        # Total fees paid
        total_fees = sum(trade['fee'] for trade in self.trades)
        
        return {
            'initial_capital': initial_value,
            'final_value': final_value,
            'total_return': total_return,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown * 100,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'win_rate_pct': win_rate * 100,
            'total_fees': total_fees,
            'trades': self.trades,
            'portfolio_history': self.portfolio_history,
            'signals_history': self.signals_history
        }
    
    def get_buy_sell_signals(self) -> Tuple[List[Dict], List[Dict]]:
        """Extract buy and sell signals for visualization."""
        buy_signals = []
        sell_signals = []
        
        for signal_entry in self.signals_history:
            if signal_entry['trade_executed']:
                if signal_entry['signal'] == 0:  # Buy
                    buy_signals.append({
                        'timestamp': signal_entry['timestamp'],
                        'price': signal_entry['price']
                    })
                elif signal_entry['signal'] == 1:  # Sell
                    sell_signals.append({
                        'timestamp': signal_entry['timestamp'],
                        'price': signal_entry['price']
                    })
        
        return buy_signals, sell_signals


def test_trading_simulator():
    """Test function for the trading simulator."""
    print("Testing Trading Simulator...")
    
    from neural_network import NetworkFactory
    
    # Create a test neural network
    nn = NetworkFactory.create_trading_network()
    
    # Create test data
    n_samples = 100
    features = np.random.randn(n_samples, 15)
    prices = 100 + np.cumsum(np.random.randn(n_samples) * 0.01)  # Random walk prices
    timestamps = list(range(n_samples))
    
    # Create simulator
    simulator = TradingSimulator(initial_capital=10000, transaction_fee=0.001)
    
    # Run simulation
    results = simulator.simulate_trading(nn, features, prices, timestamps)
    
    print(f"Initial Capital: ${results['initial_capital']:.2f}")
    print(f"Final Value: ${results['final_value']:.2f}")
    print(f"Total Return: {results['total_return_pct']:.2f}%")
    print(f"Total Trades: {results['total_trades']}")
    print(f"Win Rate: {results['win_rate_pct']:.2f}%")
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.4f}")
    print(f"Max Drawdown: {results['max_drawdown_pct']:.2f}%")
    
    print("Trading Simulator test completed successfully!")


if __name__ == "__main__":
    test_trading_simulator()
