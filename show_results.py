#!/usr/bin/env python3
"""
Display the generated charts and results from the trading bot analysis.
"""

import os
import glob
from pathlib import Path

def show_latest_results():
    """Show the most recent results from the trading bot."""
    results_dir = Path("results")
    
    if not results_dir.exists():
        print("No results directory found. Please run the trading bot first.")
        return
    
    # Find the most recent files
    evolution_files = list(results_dir.glob("evolution_progress_*.png"))
    trading_files = list(results_dir.glob("trading_results_*.png"))
    indicators_files = list(results_dir.glob("technical_indicators_*.png"))
    report_files = list(results_dir.glob("summary_report_*.txt"))
    
    if not evolution_files:
        print("No result files found. Please run the trading bot first.")
        return
    
    # Get the most recent files (by modification time)
    latest_evolution = max(evolution_files, key=os.path.getmtime)
    latest_trading = max(trading_files, key=os.path.getmtime) if trading_files else None
    latest_indicators = max(indicators_files, key=os.path.getmtime) if indicators_files else None
    latest_report = max(report_files, key=os.path.getmtime) if report_files else None
    
    print("CRYPTOCURRENCY TRADING BOT - RESULTS VIEWER")
    print("=" * 60)
    
    # Show the summary report
    if latest_report:
        print("SUMMARY REPORT:")
        print("-" * 30)
        with open(latest_report, 'r') as f:
            print(f.read())
    
    print("\nGENERATED CHARTS:")
    print("-" * 30)
    
    # List available charts
    charts = []
    if latest_evolution:
        charts.append(f"1. Evolution Progress: {latest_evolution.name}")
    if latest_trading:
        charts.append(f"2. Trading Results: {latest_trading.name}")
    if latest_indicators:
        charts.append(f"3. Technical Indicators: {latest_indicators.name}")
    
    for chart in charts:
        print(chart)
    
    print(f"\nAll charts are saved in the '{results_dir}' directory.")
    print("You can open them with any image viewer or web browser.")
    
    # Try to open the charts automatically (Windows)
    try:
        import subprocess
        import sys
        
        if sys.platform == "win32":
            print("\nAttempting to open charts automatically...")
            if latest_evolution:
                subprocess.run(["start", str(latest_evolution)], shell=True, check=False)
            if latest_trading:
                subprocess.run(["start", str(latest_trading)], shell=True, check=False)
            if latest_indicators:
                subprocess.run(["start", str(latest_indicators)], shell=True, check=False)
    except Exception as e:
        print(f"Could not open charts automatically: {e}")
        print("Please open the chart files manually from the results directory.")

def list_all_results():
    """List all available result files."""
    results_dir = Path("results")
    
    if not results_dir.exists():
        print("No results directory found.")
        return
    
    print("ALL AVAILABLE RESULTS:")
    print("=" * 40)
    
    # Group files by type
    file_types = {
        "Evolution Progress": "evolution_progress_*.png",
        "Trading Results": "trading_results_*.png", 
        "Technical Indicators": "technical_indicators_*.png",
        "Summary Reports": "summary_report_*.txt",
        "Model Files": "best_model_*.npy"
    }
    
    for file_type, pattern in file_types.items():
        files = list(results_dir.glob(pattern))
        if files:
            print(f"\n{file_type}:")
            for file in sorted(files, key=os.path.getmtime, reverse=True):
                mod_time = os.path.getmtime(file)
                from datetime import datetime
                time_str = datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M:%S")
                print(f"  {file.name} (created: {time_str})")

def main():
    """Main function."""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--list":
        list_all_results()
    else:
        show_latest_results()

if __name__ == "__main__":
    main()
