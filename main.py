#!/usr/bin/env python3
"""
Cryptocurrency Trading Bot using Genetic Algorithm to optimize Neural Network parameters.

This application implements a complete trading system that:
1. Loads and preprocesses cryptocurrency OHLCV data
2. Uses a genetic algorithm to evolve neural network parameters
3. Evaluates trading performance with realistic transaction costs
4. Visualizes results with comprehensive charts and metrics

Author: AI Assistant
Date: 2024
"""

import os
import sys
import time
import numpy as np
from datetime import datetime
from typing import Dict, Any

# Import our custom modules
from data_processor import DataProcessor
from neural_network import NetworkFactory
from genetic_algorithm import GeneticAlgorithm
from fitness_evaluator import FitnessEvaluator
from trading_simulator import TradingSimulator
from visualizer import TradingVisualizer

class CryptoTradingBot:
    """
    Main class for the cryptocurrency trading bot using genetic algorithms.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the trading bot with configuration."""
        # Default configuration
        self.config = {
            'data_path': 'data/2f1enAZ2gpuoa2ALhxuTnYbfBwEtEiyYgTkyGREHpump_1m.csv',
            'train_split': 0.8,
            'initial_capital': 10000.0,
            'transaction_fee': 0.001,  # 0.1%
            'population_size': 200,
            'generations': 200,
            'mutation_rate': 0.1,
            'crossover_rate': 0.8,
            'elite_percentage': 0.1,
            'tournament_size': 3,
            'save_results': True,
            'results_dir': 'results',
            'verbose': True
        }

        # Update with user config if provided
        if config:
            self.config.update(config)

        # Initialize components
        self.data_processor = None
        self.genetic_algorithm = None
        self.fitness_evaluator = None
        self.visualizer = TradingVisualizer()

        # Results storage
        self.processed_data = None
        self.evolution_results = None
        self.best_individual = None
        self.final_simulation_results = None

        # Create results directory
        if self.config['save_results']:
            os.makedirs(self.config['results_dir'], exist_ok=True)

    def load_and_process_data(self):
        """Load and preprocess the cryptocurrency data."""
        print("=" * 60)
        print("LOADING AND PROCESSING DATA")
        print("=" * 60)

        # Initialize data processor with folder paths
        self.data_processor = DataProcessor(
            data_path=self.config.get('data_path'),
            data_folder="data",
            eval_folder="evalData"
        )

        # Show available files
        data_files = self.data_processor.get_available_data_files()
        eval_files = self.data_processor.get_available_eval_files()

        print(f"Available training files: {len(data_files)}")
        for i, file in enumerate(data_files[:5]):  # Show first 5
            print(f"  {i+1}. {os.path.basename(file)}")
        if len(data_files) > 5:
            print(f"  ... and {len(data_files) - 5} more files")

        print(f"Available evaluation files: {len(eval_files)}")
        for file in eval_files:
            print(f"  - {os.path.basename(file)}")

        # Load initial data for scaler fitting
        print("\nLoading initial data for scaler setup...")
        raw_data = self.data_processor.load_random_data()
        if raw_data is None:
            raise ValueError("Failed to load initial data")

        # Process data with technical indicators (this will fit the scaler)
        self.processed_data = self.data_processor.process_data(
            train_split=self.config['train_split']
        )

        print(f"Initial training samples: {len(self.processed_data['train_features'])}")
        print(f"Initial testing samples: {len(self.processed_data['test_features'])}")
        print(f"Features: {len(self.processed_data['feature_names'])}")
        print("Feature names:", self.processed_data['feature_names'])
        print("\nNote: Each generation will use different training data!")

        return self.processed_data

    def setup_genetic_algorithm(self):
        """Setup the genetic algorithm and fitness evaluator."""
        print("\n" + "=" * 60)
        print("SETTING UP GENETIC ALGORITHM")
        print("=" * 60)

        # Initialize fitness evaluator with data processor for dynamic data loading
        self.fitness_evaluator = FitnessEvaluator(
            data_processor=self.data_processor,
            initial_capital=self.config['initial_capital'],
            transaction_fee=self.config['transaction_fee']
        )

        # Initialize genetic algorithm
        self.genetic_algorithm = GeneticAlgorithm(
            population_size=self.config['population_size'],
            mutation_rate=self.config['mutation_rate'],
            crossover_rate=self.config['crossover_rate'],
            elite_percentage=self.config['elite_percentage'],
            tournament_size=self.config['tournament_size']
        )

        print(f"Population size: {self.config['population_size']}")
        print(f"Generations: {self.config['generations']}")
        print(f"Mutation rate: {self.config['mutation_rate']}")
        print(f"Crossover rate: {self.config['crossover_rate']}")
        print(f"Elite percentage: {self.config['elite_percentage']}")

        # Create sample network to show architecture
        sample_network = NetworkFactory.create_trading_network()
        print(f"Neural network architecture: {sample_network.layer_sizes}")
        print(f"Total parameters to optimize: {sample_network.get_parameter_count()}")

    def run_evolution(self):
        """Run the genetic algorithm evolution process."""
        print("\n" + "=" * 60)
        print("RUNNING GENETIC ALGORITHM EVOLUTION")
        print("=" * 60)

        start_time = time.time()

        # Run evolution
        self.evolution_results = self.genetic_algorithm.evolve(
            fitness_evaluator=self.fitness_evaluator,
            generations=self.config['generations'],
            verbose=self.config['verbose']
        )

        evolution_time = time.time() - start_time

        print(f"\nEvolution completed in {evolution_time:.2f} seconds")

        # Get best individual
        self.best_individual = self.genetic_algorithm.get_best_individual()

        if self.best_individual and 'best_individual_evaluation' in self.evolution_results:
            best_eval = self.evolution_results['best_individual_evaluation']
            print(f"\nBest Individual Performance:")
            print(f"Fitness Score: {best_eval['fitness']:.4f}")
            print(f"Total Return: {best_eval['total_return_pct']:.2f}%")
            print(f"Sharpe Ratio: {best_eval['sharpe_ratio']:.4f}")
            print(f"Max Drawdown: {best_eval['max_drawdown_pct']:.2f}%")
            print(f"Win Rate: {best_eval['win_rate_pct']:.2f}%")
            print(f"Total Trades: {best_eval['total_trades']}")

        return self.evolution_results

    def test_best_individual(self):
        """Test the best individual on evaluation data from evalData folder."""
        print("\n" + "=" * 60)
        print("TESTING BEST INDIVIDUAL ON EVALUATION DATA")
        print("=" * 60)

        if self.best_individual is None:
            print("No best individual available for testing")
            return None

        try:
            # Load evaluation data from evalData folder
            eval_data = self.data_processor.load_eval_data()
            if eval_data is None:
                raise ValueError("Failed to load evaluation data")

            # Process evaluation data
            processed_eval_df = self.data_processor.calculate_technical_indicators(eval_data)
            eval_features = self.data_processor.prepare_features(processed_eval_df)

            # Remove rows with NaN values
            valid_indices = ~np.isnan(eval_features).any(axis=1)
            eval_features = eval_features[valid_indices]
            processed_eval_df = processed_eval_df[valid_indices].reset_index(drop=True)

            # Scale features using existing scaler
            eval_features_scaled = self.data_processor.scaler.transform(eval_features)

            eval_prices = processed_eval_df['close'].values
            eval_timestamps = processed_eval_df['unixTime'].values

            print(f"Evaluation data: {len(eval_features_scaled)} samples")
            print(f"Date range: {processed_eval_df['datetime'].min()} to {processed_eval_df['datetime'].max()}")

            # Create simulator for evaluation data
            test_simulator = TradingSimulator(
                initial_capital=self.config['initial_capital'],
                transaction_fee=self.config['transaction_fee']
            )

            # Run simulation on evaluation data
            self.final_simulation_results = test_simulator.simulate_trading(
                neural_network=self.best_individual,
                features=eval_features_scaled,
                prices=eval_prices,
                timestamps=eval_timestamps
            )

            # Store evaluation data for visualization
            self.eval_data = {
                'processed_df': processed_eval_df,
                'features': eval_features_scaled,
                'prices': eval_prices,
                'timestamps': eval_timestamps
            }

        except Exception as e:
            print(f"Error loading evaluation data: {e}")
            print("Falling back to test split from training data...")

            # Fallback to original method
            test_features = self.processed_data['test_features']
            split_idx = self.processed_data['split_idx']
            test_df = self.processed_data['processed_df'].iloc[split_idx:]
            test_prices = test_df['close'].values
            test_timestamps = test_df['unixTime'].values

            test_simulator = TradingSimulator(
                initial_capital=self.config['initial_capital'],
                transaction_fee=self.config['transaction_fee']
            )

            self.final_simulation_results = test_simulator.simulate_trading(
                neural_network=self.best_individual,
                features=test_features,
                prices=test_prices,
                timestamps=test_timestamps
            )

            self.eval_data = {
                'processed_df': test_df,
                'features': test_features,
                'prices': test_prices,
                'timestamps': test_timestamps
            }

        print("Test Results:")
        print(f"Initial Capital: ${self.final_simulation_results['initial_capital']:,.2f}")
        print(f"Final Value: ${self.final_simulation_results['final_value']:,.2f}")
        print(f"Total Return: {self.final_simulation_results['total_return_pct']:.2f}%")
        print(f"Sharpe Ratio: {self.final_simulation_results['sharpe_ratio']:.4f}")
        print(f"Max Drawdown: {self.final_simulation_results['max_drawdown_pct']:.2f}%")
        print(f"Win Rate: {self.final_simulation_results['win_rate_pct']:.2f}%")
        print(f"Total Trades: {self.final_simulation_results['total_trades']}")
        print(f"Total Fees: ${self.final_simulation_results['total_fees']:.2f}")

        return self.final_simulation_results

    def generate_visualizations(self):
        """Generate all visualization plots."""
        print("\n" + "=" * 60)
        print("GENERATING VISUALIZATIONS")
        print("=" * 60)

        if self.config['save_results']:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Evolution progress plot
            evolution_plot_path = os.path.join(
                self.config['results_dir'], f'evolution_progress_{timestamp}.png'
            )
            self.visualizer.plot_evolution_progress(
                self.evolution_results, save_path=evolution_plot_path
            )

            # Trading results plot
            if self.final_simulation_results:
                trading_plot_path = os.path.join(
                    self.config['results_dir'], f'trading_results_{timestamp}.png'
                )
                # Use evaluation data if available, otherwise fall back to processed data
                plot_df = self.eval_data['processed_df'] if hasattr(self, 'eval_data') else self.processed_data['processed_df']
                split_idx = None if hasattr(self, 'eval_data') else self.processed_data['split_idx']

                self.visualizer.plot_trading_results(
                    processed_df=plot_df,
                    simulation_results=self.final_simulation_results,
                    split_idx=split_idx,
                    save_path=trading_plot_path
                )

            # Technical indicators plot
            indicators_plot_path = os.path.join(
                self.config['results_dir'], f'technical_indicators_{timestamp}.png'
            )
            # Use evaluation data if available
            indicators_df = self.eval_data['processed_df'] if hasattr(self, 'eval_data') else self.processed_data['processed_df']
            self.visualizer.plot_technical_indicators(
                indicators_df,
                start_idx=max(0, len(indicators_df) - 500),
                save_path=indicators_plot_path
            )

            # Summary report
            if self.final_simulation_results:
                report_path = os.path.join(
                    self.config['results_dir'], f'summary_report_{timestamp}.txt'
                )
                self.visualizer.create_summary_report(
                    self.evolution_results, self.final_simulation_results, save_path=report_path
                )
        else:
            # Show plots without saving
            self.visualizer.plot_evolution_progress(self.evolution_results)
            if self.final_simulation_results:
                plot_df = self.eval_data['processed_df'] if hasattr(self, 'eval_data') else self.processed_data['processed_df']
                split_idx = None if hasattr(self, 'eval_data') else self.processed_data['split_idx']

                self.visualizer.plot_trading_results(
                    processed_df=plot_df,
                    simulation_results=self.final_simulation_results,
                    split_idx=split_idx
                )

            indicators_df = self.eval_data['processed_df'] if hasattr(self, 'eval_data') else self.processed_data['processed_df']
            self.visualizer.plot_technical_indicators(
                indicators_df,
                start_idx=max(0, len(indicators_df) - 500)
            )
            if self.final_simulation_results:
                self.visualizer.create_summary_report(
                    self.evolution_results, self.final_simulation_results
                )

    def save_best_model(self):
        """Save the best neural network model."""
        if self.best_individual and self.config['save_results']:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_path = os.path.join(
                self.config['results_dir'], f'best_model_{timestamp}.npy'
            )
            self.genetic_algorithm.save_best_individual(model_path)

    def run_complete_analysis(self):
        """Run the complete trading bot analysis pipeline."""
        print("CRYPTOCURRENCY TRADING BOT - GENETIC ALGORITHM OPTIMIZATION")
        print("=" * 80)

        try:
            # Step 1: Load and process data
            self.load_and_process_data()

            # Step 2: Setup genetic algorithm
            self.setup_genetic_algorithm()

            # Step 3: Run evolution
            self.run_evolution()

            # Step 4: Test best individual
            self.test_best_individual()

            # Step 5: Generate visualizations
            self.generate_visualizations()

            # Step 6: Save best model
            self.save_best_model()

            print("\n" + "=" * 80)
            print("ANALYSIS COMPLETED SUCCESSFULLY!")
            print("=" * 80)

            return {
                'processed_data': self.processed_data,
                'evolution_results': self.evolution_results,
                'best_individual': self.best_individual,
                'test_results': self.final_simulation_results
            }

        except Exception as e:
            print(f"\nError during analysis: {e}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """Main function to run the cryptocurrency trading bot."""
    # Configuration
    config = {
        'population_size': 50,      # Reduce for faster testing
        'generations': 100,         # Reduce for faster testing
        'mutation_rate': 0.1,
        'crossover_rate': 0.8,
        'elite_percentage': 0.1,
        'initial_capital': 10000.0,
        'transaction_fee': 0.001,   # 0.1% per trade
        'train_split': 0.8,
        'save_results': True,
        'verbose': True
    }

    # Create and run the trading bot
    bot = CryptoTradingBot(config)
    results = bot.run_complete_analysis()

    if results:
        print("\nTrading bot analysis completed successfully!")
        print("Check the 'results' directory for saved plots and reports.")
    else:
        print("\nTrading bot analysis failed!")


if __name__ == "__main__":
    main()
