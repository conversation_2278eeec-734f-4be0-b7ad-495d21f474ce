#!/usr/bin/env python3
"""
Quick test run of the cryptocurrency trading bot with reduced parameters for demonstration.
"""

from main import CryptoTradingBot

def main():
    """Run the trading bot with reduced parameters for faster execution."""
    print("CRYPTOCURRENCY TRADING BOT - QUICK DEMO")
    print("=" * 60)
    
    # Reduced configuration for faster execution
    config = {
        'population_size': 20,      # Smaller population
        'generations': 20,          # Fewer generations
        'mutation_rate': 0.15,
        'crossover_rate': 0.8,
        'elite_percentage': 0.15,
        'initial_capital': 10000.0,
        'transaction_fee': 0.001,   # 0.1% per trade
        'train_split': 0.8,
        'save_results': True,
        'verbose': True
    }
    
    print("Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print()
    
    # Create and run the trading bot
    bot = CryptoTradingBot(config)
    results = bot.run_complete_analysis()
    
    if results:
        print("\n" + "=" * 60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("Check the 'results' directory for:")
        print("- Evolution progress charts")
        print("- Trading signals visualization")
        print("- Technical indicators plots")
        print("- Performance summary report")
        print("- Best model parameters")
    else:
        print("\nDemo failed! Check error messages above.")

if __name__ == "__main__":
    main()
