# Cryptocurrency Trading Bot with Genetic Algorithm

A sophisticated cryptocurrency trading bot that uses genetic algorithms to optimize neural network parameters for automated trading decisions. The system analyzes OHLCV (Open, High, Low, Close, Volume) data and generates buy/sell/hold signals based on technical indicators.

## 🚀 Features

- **Genetic Algorithm Optimization**: Evolves neural network weights and biases without traditional backpropagation
- **Technical Analysis**: Incorporates RSI, MACD, Moving Averages, Bollinger Bands, and volatility indicators
- **Realistic Trading Simulation**: Includes transaction fees, position management, and risk metrics
- **Comprehensive Visualization**: Charts showing evolution progress, trading signals, and performance metrics
- **Risk Management**: Calculates Sharpe ratio, maximum drawdown, and win rates

## 🏗️ Architecture

### Neural Network Structure
- **Input Layer**: 12 features (technical indicators + position state)
- **Hidden Layer 1**: 16 neurons with ReLU activation
- **Hidden Layer 2**: 8 neurons with ReLU activation
- **Output Layer**: 3 neurons with Softmax activation (Buy/Sell/Hold probabilities)

### Genetic Algorithm Parameters
- **Population Size**: 50 individuals
- **Generations**: 100 (configurable)
- **Mutation Rate**: 10%
- **Crossover Rate**: 80%
- **Elite Preservation**: Top 10% survive each generation
- **Selection**: Tournament selection with size 3

### Trading Parameters
- **Transaction Fee**: 0.1% per trade
- **Initial Capital**: $10,000
- **Position Sizing**: 100% of available capital per trade

## 📊 Features

The system uses 12 carefully selected features for neural network input:

### Technical Indicators (10 features):
1. **SMA 5**: Short-term trend (5-period Simple Moving Average)
2. **SMA 10**: Medium-term trend (10-period Simple Moving Average)
3. **SMA 20**: Long-term trend (20-period Simple Moving Average)
4. **EMA 12**: Fast exponential moving average (12-period)
5. **EMA 26**: Slow exponential moving average (26-period)
6. **RSI**: Relative Strength Index (14-period momentum oscillator)
7. **MACD**: Moving Average Convergence Divergence line
8. **MACD Signal**: MACD signal line (9-period EMA of MACD)
9. **Awesome Oscillator**: Difference between 5-period and 34-period SMA of median price
10. **BB Position**: Normalized position within Bollinger Bands (0-1 scale)

### Position State Features (2 features):
11. **Position State**: Binary indicator (0 = no position, 1 = holding position)
12. **Price Change Since Purchase**: Percentage change from purchase price (0 if no position)

## 🎯 Fitness Function

The genetic algorithm optimizes a composite fitness score considering:

- **Total Return** (40% weight): Portfolio performance
- **Sharpe Ratio** (30% weight): Risk-adjusted returns
- **Maximum Drawdown** (20% weight): Risk management (penalty)
- **Trading Frequency** (10% weight): Prevents overtrading (penalty)

## 📁 Project Structure

```
├── main.py                 # Main orchestration script
├── data_processor.py       # Data loading and preprocessing
├── neural_network.py       # Neural network implementation
├── genetic_algorithm.py    # Genetic algorithm engine
├── fitness_evaluator.py    # Trading performance evaluation
├── trading_simulator.py    # Trading simulation with fees
├── visualizer.py          # Chart generation and reporting
├── run_trading_bot.py     # Quick demo script
├── run_full_analysis.py   # Comprehensive analysis script
├── show_results.py        # Results viewer
├── requirements.txt       # Python dependencies
├── data/                  # Cryptocurrency data directory
└── results/              # Generated charts and reports
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install numpy pandas matplotlib scikit-learn tqdm
```

### 2. Run Quick Demo (20 generations)
```bash
python run_trading_bot.py
```

### 3. Run Full Analysis (50 generations)
```bash
python run_full_analysis.py
```

### 4. View Results
```bash
python show_results.py
```

## 📈 Generated Outputs

The system generates several files in the `results/` directory:

### Charts
- **Evolution Progress**: Fitness evolution over generations
- **Trading Results**: Price chart with buy/sell signals and portfolio performance
- **Technical Indicators**: RSI, MACD, moving averages, and volume analysis

### Reports
- **Summary Report**: Comprehensive performance metrics
- **Best Model**: Saved neural network parameters (.npy file)

## 🔧 Configuration

You can customize the system by modifying the configuration in the run scripts:

```python
config = {
    'population_size': 50,      # Number of individuals in population
    'generations': 100,         # Number of evolution generations
    'mutation_rate': 0.1,       # Probability of parameter mutation
    'crossover_rate': 0.8,      # Probability of crossover
    'elite_percentage': 0.1,    # Percentage of elite individuals preserved
    'initial_capital': 10000.0, # Starting capital for trading
    'transaction_fee': 0.001,   # Transaction fee (0.1%)
    'train_split': 0.8,         # Training/testing data split
    'save_results': True,       # Save charts and reports
    'verbose': True             # Print progress during evolution
}
```

## 📊 Performance Metrics

The system evaluates trading performance using:

- **Total Return**: Percentage gain/loss from initial capital
- **Sharpe Ratio**: Risk-adjusted return metric
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Total Trades**: Number of buy/sell transactions executed
- **Transaction Costs**: Total fees paid during trading

## ⚠️ Important Notes

### Risk Disclaimer
This is an educational/research project. Cryptocurrency trading involves significant risk. Never trade with money you cannot afford to lose. Always implement proper risk management and consider market conditions.

### Data Requirements
- The system expects 1-minute OHLCV data in CSV format
- Minimum recommended data: 1000+ data points for meaningful analysis
- Data should be sorted chronologically

### Performance Considerations
- Evolution time scales with population size and generations
- Larger populations generally produce better results but take longer
- Consider using smaller parameters for initial testing

## 🔬 Algorithm Details

### Genetic Operations

1. **Initialization**: Random neural network parameters with Xavier initialization
2. **Selection**: Tournament selection chooses parents for reproduction
3. **Crossover**: Uniform crossover combines parent parameters
4. **Mutation**: Gaussian noise added to parameters with specified probability
5. **Elite Preservation**: Best individuals automatically survive to next generation

### Trading Logic

1. **Signal Generation**: Neural network outputs buy/sell/hold probabilities
2. **Position Management**:
   - Buy: Use all available capital when no position exists
   - Sell: Close entire position when holding cryptocurrency
   - Hold: No action taken
3. **Fee Calculation**: 0.1% fee applied to all transactions
4. **Performance Tracking**: Real-time portfolio value and trade history

## 🎨 Visualization Features

The system generates publication-quality charts showing:

- Evolution progress with fitness metrics over generations
- Price charts with clear buy/sell signal markers
- Technical indicator overlays and analysis
- Portfolio performance and drawdown visualization
- Returns distribution and risk metrics

## 🤝 Contributing

This project demonstrates advanced concepts in:
- Genetic algorithms and evolutionary computation
- Neural networks without backpropagation
- Quantitative finance and algorithmic trading
- Technical analysis and risk management
- Data visualization and reporting

Feel free to experiment with different:
- Neural network architectures
- Genetic algorithm parameters
- Technical indicators
- Fitness function weights
- Risk management strategies

## 📚 References

- Genetic Algorithms in Finance and Investment
- Technical Analysis of Financial Markets
- Quantitative Risk Management
- Evolutionary Neural Networks
- Algorithmic Trading Strategies

---

**Created by**: AI Assistant
**Date**: 2024
**Purpose**: Educational demonstration of genetic algorithms in quantitative finance
