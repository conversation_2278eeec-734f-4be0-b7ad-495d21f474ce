#!/usr/bin/env python3
"""
Cryptocurrency trading bot with position-aware neural network.
The network knows about current position state and profit/loss.
"""

from main import CryptoTradingBot
import os

def main():
    """Run the position-aware trading bot."""
    print("CRYPTOCURRENCY TRADING BOT - POSITION AWARE")
    print("=" * 70)
    print("🧠 Neural network knows about current position state")
    print("📈 Tracks profit/loss since purchase")
    print("🎯 Better decision making with context awareness")
    print("=" * 70)
    
    # Check available data
    data_files = [f for f in os.listdir("data") if f.endswith('.csv')]
    eval_files = [f for f in os.listdir("evalData") if f.endswith('.csv')]
    
    print(f"Available training files: {len(data_files)}")
    print(f"Available evaluation files: {len(eval_files)}")
    
    # Position-aware configuration
    config = {
        'population_size': 100,      # Moderate population
        'generations': 100,          # Each generation uses different data
        'mutation_rate': 0.1,       # Standard mutation rate
        'crossover_rate': 0.8,      # High crossover rate
        'elite_percentage': 0.15,   # Keep top 15%
        'initial_capital': 10000.0,
        'transaction_fee': 0.001,   # 0.1% per trade
        'train_split': 0.8,         # Not used in dynamic mode
        'save_results': True,
        'verbose': True
    }
    
    print("\nPosition-Aware Features:")
    features = [
        "🔍 12 input features (10 technical + 2 position state)",
        "📊 Technical indicators: SMA, EMA, RSI, MACD, AO, BB",
        "🎯 Position state: Currently holding position (0/1)",
        "💰 Price change: % profit/loss since purchase",
        "🧠 Context-aware decision making",
        "🔄 Dynamic feature generation during trading"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print(f"\nNeural Network Architecture:")
    print(f"  Input Layer: 12 features")
    print(f"  Hidden Layer 1: 16 neurons (ReLU)")
    print(f"  Hidden Layer 2: 8 neurons (ReLU)")
    print(f"  Output Layer: 3 neurons (Softmax - Buy/Sell/Hold)")
    print(f"  Total Parameters: ~400 (optimized by genetic algorithm)")
    
    print(f"\nPosition State Logic:")
    print(f"  • When no position: position_state=0, price_change=0")
    print(f"  • When holding: position_state=1, price_change=% since purchase")
    print(f"  • Network can learn to:")
    print(f"    - Hold profitable positions longer")
    print(f"    - Cut losses quickly")
    print(f"    - Avoid buying when already holding")
    print(f"    - Time exits based on profit/loss")
    
    print(f"\nConfiguration:")
    for key, value in config.items():
        if key != 'train_split':
            print(f"  {key}: {value}")
    print()
    
    # Create and run the trading bot
    bot = CryptoTradingBot(config)
    results = bot.run_complete_analysis()
    
    if results:
        print("\n" + "=" * 70)
        print("POSITION-AWARE TRAINING COMPLETED!")
        print("=" * 70)
        
        # Print detailed insights
        if results['test_results']:
            test_results = results['test_results']
            evolution_results = results['evolution_results']
            
            print("\nPOSITION-AWARE RESULTS:")
            print("-" * 40)
            
            # Show training performance
            final_gen = evolution_results['generation_stats'][-1]
            print(f"Training Performance:")
            print(f"  Final Best Return: {final_gen['best_return']:.2f}%")
            print(f"  Final Best Sharpe: {final_gen['best_sharpe']:.4f}")
            print(f"  Final Best Fitness: {final_gen['best_fitness']:.4f}")
            
            print(f"\nEvaluation Performance (with position awareness):")
            print(f"  Total Return: {test_results['total_return_pct']:.2f}%")
            print(f"  Sharpe Ratio: {test_results['sharpe_ratio']:.4f}")
            print(f"  Max Drawdown: {test_results['max_drawdown_pct']:.2f}%")
            print(f"  Win Rate: {test_results['win_rate_pct']:.1f}%")
            print(f"  Total Trades: {test_results['total_trades']}")
            
            # Position-aware analysis
            print(f"\nPOSITION-AWARE ANALYSIS:")
            print("-" * 30)
            
            if test_results['total_trades'] > 0:
                avg_return_per_trade = test_results['total_return_pct'] / test_results['total_trades']
                print(f"✓ Active trading: {test_results['total_trades']} trades executed")
                print(f"  Average return per trade: {avg_return_per_trade:.2f}%")
                
                if test_results['win_rate_pct'] > 50:
                    print(f"✓ Positive win rate: {test_results['win_rate_pct']:.1f}%")
                    print("  Network learned to identify profitable opportunities")
                else:
                    print(f"⚠ Win rate: {test_results['win_rate_pct']:.1f}%")
                    print("  Network may be cutting losses effectively")
                
                if test_results['sharpe_ratio'] > 1.0:
                    print("✓ Excellent risk-adjusted returns (Sharpe > 1.0)")
                    print("  Position awareness improved risk management")
                elif test_results['sharpe_ratio'] > 0.5:
                    print("~ Good risk-adjusted returns (Sharpe > 0.5)")
                else:
                    print("⚠ Low risk-adjusted returns")
                
                if test_results['max_drawdown_pct'] < 15:
                    print(f"✓ Controlled drawdown: {test_results['max_drawdown_pct']:.2f}%")
                    print("  Position awareness helped limit losses")
                else:
                    print(f"⚠ High drawdown: {test_results['max_drawdown_pct']:.2f}%")
                
            else:
                print("⚠ No trades executed on evaluation data")
                print("  Network may have learned overly conservative strategy")
            
            # Compare with buy-and-hold
            if len(results['test_results'].get('portfolio_history', [])) > 0:
                portfolio_history = results['test_results']['portfolio_history']
                if len(portfolio_history) > 1:
                    first_price = portfolio_history[0].get('price', 0)
                    last_price = portfolio_history[-1].get('price', 0)
                    if first_price > 0:
                        buy_hold_return = ((last_price - first_price) / first_price) * 100
                        print(f"\nBenchmark Comparison:")
                        print(f"  Buy & Hold Return: {buy_hold_return:.2f}%")
                        print(f"  Bot Return: {test_results['total_return_pct']:.2f}%")
                        if test_results['total_return_pct'] > buy_hold_return:
                            print("✓ Bot outperformed buy & hold strategy")
                        else:
                            print("⚠ Bot underperformed buy & hold strategy")
        
        print(f"\nPosition-Aware Benefits:")
        print("• Network knows when it's already holding a position")
        print("• Can track profit/loss in real-time")
        print("• Makes context-aware buy/sell decisions")
        print("• Potentially better risk management")
        print("• More realistic trading behavior")
        
        print(f"\nGenerated Analysis:")
        print("📈 Evolution progress across different market conditions")
        print("📊 Trading signals with position-aware decision making")
        print("📉 Technical indicators with position state overlay")
        print("📋 Comprehensive performance analysis")
        print("🤖 Position-aware neural network model")
        
        print(f"\nNext Steps:")
        print("1. Analyze how position awareness affected trading decisions")
        print("2. Compare performance with position-unaware version")
        print("3. Study profit/loss tracking effectiveness")
        print("4. Consider additional position-related features")
        print("5. Test on different market conditions")
        
    else:
        print("\nPosition-aware training failed! Check error messages above.")

if __name__ == "__main__":
    main()
