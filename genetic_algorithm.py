import numpy as np
import random
from typing import List, <PERSON><PERSON>, Dict, Any
from neural_network import NeuralNetwork, NetworkFactory
from fitness_evaluator import FitnessEvaluator

class GeneticAlgorithm:
    """
    Genetic Algorithm for evolving neural network parameters for cryptocurrency trading.
    """

    def __init__(self, population_size: int = 50, mutation_rate: float = 0.1,
                 crossover_rate: float = 0.8, elite_percentage: float = 0.1,
                 tournament_size: int = 3):
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.elite_percentage = elite_percentage
        self.tournament_size = tournament_size

        # Evolution tracking
        self.generation = 0
        self.population = []
        self.fitness_history = []
        self.best_individual_history = []

        # Statistics
        self.best_fitness = -float('inf')
        self.best_individual = None

    def initialize_population(self) -> List[NeuralNetwork]:
        """Initialize a random population of neural networks."""
        population = []

        for _ in range(self.population_size):
            # Create a new neural network with random parameters
            nn = NetworkFactory.create_trading_network()

            # Add some random variation to initial parameters
            params = nn.get_parameters()
            params += np.random.normal(0, 0.1, len(params))
            nn.set_parameters(params)

            population.append(nn)

        self.population = population
        return population

    def tournament_selection(self, evaluations: List[Dict[str, Any]]) -> NeuralNetwork:
        """Select an individual using tournament selection."""
        # Select random individuals for tournament
        tournament_indices = random.sample(range(len(evaluations)), self.tournament_size)
        tournament_evaluations = [evaluations[i] for i in tournament_indices]

        # Find the best individual in the tournament
        valid_evaluations = [eval_result for eval_result in tournament_evaluations
                           if 'error' not in eval_result]

        if not valid_evaluations:
            # If no valid evaluations, return a random individual
            return self.population[random.choice(tournament_indices)]

        best_eval = max(valid_evaluations, key=lambda x: x['fitness'])
        best_index = best_eval['individual_id']

        return self.population[best_index]

    def uniform_crossover(self, parent1: NeuralNetwork, parent2: NeuralNetwork) -> Tuple[NeuralNetwork, NeuralNetwork]:
        """Perform uniform crossover between two neural networks."""
        # Get parameters from both parents
        params1 = parent1.get_parameters()
        params2 = parent2.get_parameters()

        # Create offspring parameters
        offspring1_params = np.zeros_like(params1)
        offspring2_params = np.zeros_like(params2)

        # Uniform crossover: randomly choose each parameter from either parent
        for i in range(len(params1)):
            if random.random() < 0.5:
                offspring1_params[i] = params1[i]
                offspring2_params[i] = params2[i]
            else:
                offspring1_params[i] = params2[i]
                offspring2_params[i] = params1[i]

        # Create offspring neural networks
        offspring1 = NetworkFactory.create_trading_network()
        offspring2 = NetworkFactory.create_trading_network()

        offspring1.set_parameters(offspring1_params)
        offspring2.set_parameters(offspring2_params)

        return offspring1, offspring2

    def gaussian_mutation(self, individual: NeuralNetwork) -> NeuralNetwork:
        """Apply Gaussian mutation to a neural network."""
        # Get parameters
        params = individual.get_parameters()

        # Apply mutation
        for i in range(len(params)):
            if random.random() < self.mutation_rate:
                # Add Gaussian noise
                mutation_strength = 0.1  # Standard deviation of mutation
                params[i] += np.random.normal(0, mutation_strength)

        # Create mutated individual
        mutated_individual = NetworkFactory.create_trading_network()
        mutated_individual.set_parameters(params)

        return mutated_individual

    def create_next_generation(self, evaluations: List[Dict[str, Any]]) -> List[NeuralNetwork]:
        """Create the next generation using selection, crossover, and mutation."""
        next_generation = []

        # Elite selection: keep the best individuals
        elite_count = int(self.population_size * self.elite_percentage)
        valid_evaluations = [eval_result for eval_result in evaluations
                           if 'error' not in eval_result]

        if valid_evaluations:
            # Sort by fitness and select elite
            sorted_evaluations = sorted(valid_evaluations, key=lambda x: x['fitness'], reverse=True)
            elite_indices = [eval_result['individual_id'] for eval_result in sorted_evaluations[:elite_count]]

            for idx in elite_indices:
                next_generation.append(self.population[idx].copy())

        # Generate remaining individuals through crossover and mutation
        while len(next_generation) < self.population_size:
            if random.random() < self.crossover_rate and len(next_generation) < self.population_size - 1:
                # Crossover
                parent1 = self.tournament_selection(evaluations)
                parent2 = self.tournament_selection(evaluations)

                offspring1, offspring2 = self.uniform_crossover(parent1, parent2)

                # Apply mutation
                offspring1 = self.gaussian_mutation(offspring1)
                offspring2 = self.gaussian_mutation(offspring2)

                next_generation.append(offspring1)
                if len(next_generation) < self.population_size:
                    next_generation.append(offspring2)
            else:
                # Mutation only
                parent = self.tournament_selection(evaluations)
                offspring = self.gaussian_mutation(parent)
                next_generation.append(offspring)

        return next_generation[:self.population_size]

    def evolve(self, fitness_evaluator: FitnessEvaluator, generations: int = 100,
               verbose: bool = True) -> Dict[str, Any]:
        """
        Run the genetic algorithm evolution process.

        Args:
            fitness_evaluator: FitnessEvaluator instance
            generations: Number of generations to evolve
            verbose: Whether to print progress

        Returns:
            Dict containing evolution results
        """
        # Initialize population if not already done
        if not self.population:
            self.initialize_population()

        evolution_results = {
            'generation_stats': [],
            'best_fitness_history': [],
            'mean_fitness_history': []
        }

        for gen in range(generations):
            self.generation = gen

            # Evaluate population with current generation number
            evaluations = fitness_evaluator.evaluate_population(self.population, generation=gen)

            # Get statistics
            stats = fitness_evaluator.get_fitness_statistics(evaluations)
            best_individual_eval = fitness_evaluator.get_best_individual(evaluations)

            # Update best individual if improved
            if 'error' not in best_individual_eval and best_individual_eval['fitness'] > self.best_fitness:
                self.best_fitness = best_individual_eval['fitness']
                self.best_individual = self.population[best_individual_eval['individual_id']].copy()

            # Record statistics
            generation_stats = {
                'generation': gen,
                'best_fitness': best_individual_eval.get('fitness', -1000),
                'mean_fitness': stats.get('mean_fitness', 0),
                'std_fitness': stats.get('std_fitness', 0),
                'best_return': best_individual_eval.get('total_return_pct', 0),
                'best_sharpe': best_individual_eval.get('sharpe_ratio', 0),
                'best_trades': best_individual_eval.get('total_trades', 0)
            }

            evolution_results['generation_stats'].append(generation_stats)
            evolution_results['best_fitness_history'].append(generation_stats['best_fitness'])
            evolution_results['mean_fitness_history'].append(generation_stats['mean_fitness'])

            # Print progress
            if verbose:
                print(f"Generation {gen:3d}: "
                      f"Best Fitness={generation_stats['best_fitness']:8.4f}, "
                      f"Mean Fitness={generation_stats['mean_fitness']:8.4f}, "
                      f"Best Return={generation_stats['best_return']:6.2f}%, "
                      f"Sharpe={generation_stats['best_sharpe']:6.4f}")

            # Create next generation (except for the last generation)
            if gen < generations - 1:
                self.population = self.create_next_generation(evaluations)

        # Final evaluation of best individual
        if self.best_individual:
            final_evaluation = fitness_evaluator.evaluate_individual(self.best_individual)
            evolution_results['best_individual_evaluation'] = final_evaluation

        evolution_results['best_individual'] = self.best_individual
        evolution_results['final_population'] = self.population

        return evolution_results

    def get_best_individual(self) -> NeuralNetwork:
        """Get the best individual found during evolution."""
        return self.best_individual

    def save_best_individual(self, filename: str):
        """Save the best individual's parameters to a file."""
        if self.best_individual:
            params = self.best_individual.get_parameters()
            np.save(filename, params)
            print(f"Best individual saved to {filename}")
        else:
            print("No best individual to save")

    def load_individual(self, filename: str) -> NeuralNetwork:
        """Load a neural network from saved parameters."""
        try:
            params = np.load(filename)
            nn = NetworkFactory.create_trading_network()
            nn.set_parameters(params)
            return nn
        except Exception as e:
            print(f"Error loading individual: {e}")
            return None


def test_genetic_algorithm():
    """Test function for the genetic algorithm."""
    print("Testing Genetic Algorithm...")

    # Create test data
    n_samples = 200
    features = np.random.randn(n_samples, 10)
    prices = 100 + np.cumsum(np.random.randn(n_samples) * 0.01)
    timestamps = list(range(n_samples))

    # Create fitness evaluator
    evaluator = FitnessEvaluator(features, prices, timestamps)

    # Create genetic algorithm
    ga = GeneticAlgorithm(population_size=10, mutation_rate=0.1, crossover_rate=0.8)

    # Run evolution for a few generations
    print("Running evolution...")
    results = ga.evolve(evaluator, generations=5, verbose=True)

    # Print final results
    if 'best_individual_evaluation' in results:
        best_eval = results['best_individual_evaluation']
        print(f"\nFinal Best Individual:")
        print(f"Fitness: {best_eval['fitness']:.4f}")
        print(f"Return: {best_eval['total_return_pct']:.2f}%")
        print(f"Sharpe Ratio: {best_eval['sharpe_ratio']:.4f}")
        print(f"Total Trades: {best_eval['total_trades']}")

    print("Genetic Algorithm test completed successfully!")


if __name__ == "__main__":
    test_genetic_algorithm()
