#!/usr/bin/env python3
"""
Cryptocurrency trading bot with dynamic training data.
Each generation uses different data files for better generalization.
"""

from main import CryptoTradingBot
import os

def main():
    """Run the trading bot with dynamic training data."""
    print("CRYPTOCURRENCY TRADING BOT - DYNAMIC TRAINING")
    print("=" * 70)
    print("🔄 Each generation uses different training data")
    print("📊 Final evaluation on separate evalData")
    print("🎯 Improved generalization and robustness")
    print("=" * 70)
    
    # Check if we have multiple data files
    data_files = [f for f in os.listdir("data") if f.endswith('.csv')]
    eval_files = [f for f in os.listdir("evalData") if f.endswith('.csv')]
    
    print(f"Available training files: {len(data_files)}")
    print(f"Available evaluation files: {len(eval_files)}")
    
    if len(data_files) < 2:
        print("⚠️  Warning: Only one training file available. Dynamic training works best with multiple files.")
    
    if len(eval_files) == 0:
        print("❌ Error: No evaluation files found in evalData folder!")
        return
    
    # Dynamic training configuration
    config = {
        'population_size': 25,      # Moderate population size
        'generations': 25,          # Each generation uses different data
        'mutation_rate': 0.12,      # Slightly higher for exploration
        'crossover_rate': 0.85,     # High crossover rate
        'elite_percentage': 0.12,   # Keep top 12%
        'initial_capital': 10000.0,
        'transaction_fee': 0.001,   # 0.1% per trade
        'train_split': 0.8,         # Not used in dynamic mode
        'save_results': True,
        'verbose': True
    }
    
    print("\nDynamic Training Features:")
    features = [
        "✓ Each generation trained on different cryptocurrency data",
        "✓ Prevents overfitting to specific market conditions", 
        "✓ Improves model robustness and generalization",
        "✓ Final evaluation on completely unseen data",
        "✓ Better real-world trading performance"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print(f"\nConfiguration:")
    for key, value in config.items():
        if key != 'train_split':  # Skip this as it's not used
            print(f"  {key}: {value}")
    print()
    
    print("Training Process:")
    print("1. Load initial data to fit feature scaler")
    print("2. For each generation:")
    print("   - Randomly select training file from 'data' folder")
    print("   - Evaluate population on this data")
    print("   - Evolve to next generation")
    print("3. Test best individual on data from 'evalData' folder")
    print("4. Generate comprehensive analysis and charts")
    print()
    
    # Create and run the trading bot
    bot = CryptoTradingBot(config)
    results = bot.run_complete_analysis()
    
    if results:
        print("\n" + "=" * 70)
        print("DYNAMIC TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        
        # Print detailed insights
        if results['test_results']:
            test_results = results['test_results']
            evolution_results = results['evolution_results']
            
            print("\nDYNAMIC TRAINING RESULTS:")
            print("-" * 40)
            
            # Show training diversity
            final_gen = evolution_results['generation_stats'][-1]
            print(f"Training Performance (across {config['generations']} different datasets):")
            print(f"  Final Best Return: {final_gen['best_return']:.2f}%")
            print(f"  Final Best Sharpe: {final_gen['best_sharpe']:.4f}")
            print(f"  Final Best Fitness: {final_gen['best_fitness']:.4f}")
            
            print(f"\nEvaluation Performance (unseen data):")
            print(f"  Total Return: {test_results['total_return_pct']:.2f}%")
            print(f"  Sharpe Ratio: {test_results['sharpe_ratio']:.4f}")
            print(f"  Max Drawdown: {test_results['max_drawdown_pct']:.2f}%")
            print(f"  Win Rate: {test_results['win_rate_pct']:.1f}%")
            print(f"  Total Trades: {test_results['total_trades']}")
            
            # Generalization assessment
            print(f"\nGENERALIZATION ASSESSMENT:")
            print("-" * 30)
            
            training_return = final_gen['best_return']
            eval_return = test_results['total_return_pct']
            
            if test_results['total_trades'] > 0:
                print("✓ Model successfully generated trading signals on unseen data")
                
                if abs(eval_return - training_return) < training_return * 0.5:
                    print("✓ Good generalization: Evaluation performance close to training")
                elif eval_return > 0:
                    print("~ Moderate generalization: Positive returns on evaluation data")
                else:
                    print("⚠ Poor generalization: Negative returns on evaluation data")
                    
                if test_results['sharpe_ratio'] > 0.5:
                    print("✓ Good risk-adjusted performance on evaluation data")
                elif test_results['sharpe_ratio'] > 0:
                    print("~ Moderate risk-adjusted performance")
                else:
                    print("⚠ Poor risk-adjusted performance")
                    
            else:
                print("⚠ Model did not generate any trades on evaluation data")
                print("  This might indicate overfitting or conservative strategy")
            
            # Trading activity analysis
            if test_results['total_trades'] > 0:
                avg_return_per_trade = test_results['total_return_pct'] / test_results['total_trades']
                print(f"\nTrading Activity:")
                print(f"  Average return per trade: {avg_return_per_trade:.2f}%")
                print(f"  Trading frequency: {test_results['total_trades']} trades")
                
                if test_results['win_rate_pct'] > 50:
                    print(f"  ✓ Positive win rate: {test_results['win_rate_pct']:.1f}%")
                else:
                    print(f"  ⚠ Low win rate: {test_results['win_rate_pct']:.1f}%")
        
        print(f"\nGenerated Analysis:")
        print("📈 Evolution progress showing fitness across different datasets")
        print("📊 Trading signals on evaluation data with buy/sell markers")
        print("📉 Technical indicators analysis on evaluation data")
        print("📋 Comprehensive performance report")
        print("🤖 Optimized neural network model parameters")
        
        print(f"\nBenefits of Dynamic Training:")
        print("• Reduced overfitting to specific market conditions")
        print("• Better adaptation to different market regimes")
        print("• More robust trading strategies")
        print("• Improved real-world performance potential")
        print("• Enhanced model generalization capabilities")
        
        print(f"\nNext Steps:")
        print("1. Analyze evaluation results vs training performance")
        print("2. Review trading signals for strategy validation")
        print("3. Consider paper trading with the robust model")
        print("4. Monitor performance on additional unseen data")
        print("5. Fine-tune parameters based on generalization results")
        
    else:
        print("\nDynamic training failed! Check error messages above.")

if __name__ == "__main__":
    main()
