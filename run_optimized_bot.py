#!/usr/bin/env python3
"""
Optimized cryptocurrency trading bot with 10 technical indicators.
Reduced feature set for better performance and interpretability.
"""

from main import CryptoTradingBot

def main():
    """Run the optimized trading bot with 10 technical indicators."""
    print("CRYPTOCURRENCY TRADING BOT - OPTIMIZED VERSION")
    print("=" * 70)
    print("Features: 10 Technical Indicators (No Raw OHLCV)")
    print("Neural Network: 10 -> 15 -> 8 -> 3")
    print("=" * 70)
    
    # Optimized configuration
    config = {
        'population_size': 30,      # Smaller population for faster evolution
        'generations': 30,          # Moderate number of generations
        'mutation_rate': 0.12,      # Slightly higher mutation for exploration
        'crossover_rate': 0.85,     # High crossover rate
        'elite_percentage': 0.15,   # Keep top 15%
        'initial_capital': 10000.0,
        'transaction_fee': 0.001,   # 0.1% per trade
        'train_split': 0.75,        # 75% training, 25% testing
        'save_results': True,
        'verbose': True
    }
    
    print("Technical Indicators Used:")
    indicators = [
        "1. SMA 5 - Short-term trend",
        "2. SMA 10 - Medium-term trend", 
        "3. SMA 20 - Long-term trend",
        "4. EMA 12 - Fast exponential moving average",
        "5. EMA 26 - Slow exponential moving average",
        "6. RSI - Relative Strength Index (momentum)",
        "7. MACD - Moving Average Convergence Divergence",
        "8. MACD Signal - MACD signal line",
        "9. Awesome Oscillator - Momentum indicator",
        "10. BB Position - Bollinger Bands position (0-1)"
    ]
    
    for indicator in indicators:
        print(f"  {indicator}")
    
    print(f"\nConfiguration:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print()
    
    print("Benefits of reduced feature set:")
    print("- Faster training and evolution")
    print("- Better interpretability of trading signals")
    print("- Reduced overfitting risk")
    print("- Focus on most important technical indicators")
    print("- Smaller neural network architecture")
    print()
    
    # Create and run the trading bot
    bot = CryptoTradingBot(config)
    results = bot.run_complete_analysis()
    
    if results:
        print("\n" + "=" * 70)
        print("OPTIMIZED ANALYSIS COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        
        # Print detailed insights
        if results['test_results']:
            test_results = results['test_results']
            evolution_results = results['evolution_results']
            
            print("\nPERFORMANCE SUMMARY:")
            print("-" * 40)
            
            # Training vs Test Performance
            final_gen = evolution_results['generation_stats'][-1]
            print(f"Training Performance:")
            print(f"  Best Return: {final_gen['best_return']:.2f}%")
            print(f"  Best Sharpe: {final_gen['best_sharpe']:.4f}")
            print(f"  Best Fitness: {final_gen['best_fitness']:.4f}")
            
            print(f"\nTest Performance:")
            print(f"  Total Return: {test_results['total_return_pct']:.2f}%")
            print(f"  Sharpe Ratio: {test_results['sharpe_ratio']:.4f}")
            print(f"  Max Drawdown: {test_results['max_drawdown_pct']:.2f}%")
            print(f"  Win Rate: {test_results['win_rate_pct']:.1f}%")
            print(f"  Total Trades: {test_results['total_trades']}")
            
            # Calculate some additional metrics
            if test_results['total_trades'] > 0:
                avg_trade_return = test_results['total_return_pct'] / test_results['total_trades']
                print(f"  Avg Return per Trade: {avg_trade_return:.2f}%")
            
            # Risk assessment
            print(f"\nRISK ASSESSMENT:")
            print("-" * 20)
            if test_results['sharpe_ratio'] > 1.0:
                print("✓ Good risk-adjusted returns (Sharpe > 1.0)")
            elif test_results['sharpe_ratio'] > 0.5:
                print("~ Moderate risk-adjusted returns (Sharpe > 0.5)")
            else:
                print("⚠ Low risk-adjusted returns (Sharpe < 0.5)")
            
            if test_results['max_drawdown_pct'] < 10:
                print("✓ Low maximum drawdown (< 10%)")
            elif test_results['max_drawdown_pct'] < 20:
                print("~ Moderate maximum drawdown (< 20%)")
            else:
                print("⚠ High maximum drawdown (> 20%)")
            
            if test_results['win_rate_pct'] > 60:
                print("✓ High win rate (> 60%)")
            elif test_results['win_rate_pct'] > 45:
                print("~ Moderate win rate (> 45%)")
            else:
                print("⚠ Low win rate (< 45%)")
        
        print(f"\nGenerated Files:")
        print("- Evolution progress chart showing fitness improvement")
        print("- Trading results with buy/sell signals on price chart")
        print("- Technical indicators visualization")
        print("- Comprehensive performance report")
        print("- Optimized neural network model")
        
        print(f"\nNEXT STEPS:")
        print("1. Analyze the technical indicators chart to understand patterns")
        print("2. Review buy/sell signals for trading logic validation")
        print("3. Consider paper trading with the optimized model")
        print("4. Monitor performance on new data")
        print("5. Adjust parameters based on market conditions")
        
    else:
        print("\nOptimized analysis failed! Check error messages above.")

if __name__ == "__main__":
    main()
