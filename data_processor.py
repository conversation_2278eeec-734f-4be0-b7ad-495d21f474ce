import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from typing import Tuple, Dict, Any

class DataProcessor:
    """
    Handles loading, preprocessing, and feature engineering for cryptocurrency data.
    """

    def __init__(self, data_path: str):
        self.data_path = data_path
        self.scaler = StandardScaler()
        self.raw_data = None
        self.processed_data = None

    def load_data(self) -> pd.DataFrame:
        """Load OHLCV data from CSV file."""
        try:
            self.raw_data = pd.read_csv(self.data_path)
            # Rename columns for easier access
            self.raw_data.columns = ['address', 'close', 'high', 'low', 'open', 'type', 'unixTime', 'volume']

            # Convert unix timestamp to datetime
            self.raw_data['datetime'] = pd.to_datetime(self.raw_data['unixTime'], unit='s')
            self.raw_data = self.raw_data.sort_values('unixTime').reset_index(drop=True)

            print(f"Loaded {len(self.raw_data)} data points")
            print(f"Date range: {self.raw_data['datetime'].min()} to {self.raw_data['datetime'].max()}")

            return self.raw_data
        except Exception as e:
            print(f"Error loading data: {e}")
            return None

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for feature engineering."""
        data = df.copy()

        # Simple Moving Averages
        data['sma_5'] = data['close'].rolling(window=5).mean()
        data['sma_10'] = data['close'].rolling(window=10).mean()
        data['sma_20'] = data['close'].rolling(window=20).mean()

        # Exponential Moving Averages
        data['ema_5'] = data['close'].ewm(span=5).mean()
        data['ema_10'] = data['close'].ewm(span=10).mean()

        # RSI (Relative Strength Index)
        data['rsi'] = self.calculate_rsi(data['close'], window=14)

        # MACD
        data['macd'], data['macd_signal'] = self.calculate_macd(data['close'])

        # Bollinger Bands
        data['bb_upper'], data['bb_lower'] = self.calculate_bollinger_bands(data['close'])

        # Price change percentage
        data['price_change'] = data['close'].pct_change()

        # Volume indicators
        data['volume_sma'] = data['volume'].rolling(window=10).mean()
        data['volume_ratio'] = data['volume'] / data['volume_sma']

        # Volatility (rolling standard deviation)
        data['volatility'] = data['close'].rolling(window=10).std()

        return data

    def calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """Calculate MACD and signal line."""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal

    def calculate_bollinger_bands(self, prices: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series]:
        """Calculate Bollinger Bands."""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return upper_band, lower_band

    def prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """Prepare feature matrix for neural network input."""
        # Select features for the neural network
        feature_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10',
            'rsi', 'macd', 'macd_signal', 'price_change', 'volatility'
        ]

        # Create feature matrix
        features = df[feature_columns].copy()

        # Handle missing values (forward fill then backward fill)
        features = features.ffill().bfill()

        return features.values

    def create_labels(self, df: pd.DataFrame, lookahead: int = 5) -> np.ndarray:
        """Create trading labels based on future price movement."""
        labels = []

        for i in range(len(df) - lookahead):
            current_price = df.iloc[i]['close']
            future_price = df.iloc[i + lookahead]['close']

            price_change = (future_price - current_price) / current_price

            # Define thresholds for buy/sell signals
            buy_threshold = 0.02   # 2% increase
            sell_threshold = -0.02  # 2% decrease

            if price_change > buy_threshold:
                labels.append(0)  # Buy signal
            elif price_change < sell_threshold:
                labels.append(1)  # Sell signal
            else:
                labels.append(2)  # Hold signal

        # Pad the end with hold signals
        labels.extend([2] * lookahead)

        return np.array(labels)

    def process_data(self, train_split: float = 0.8) -> Dict[str, Any]:
        """Complete data processing pipeline."""
        if self.raw_data is None:
            self.load_data()

        # Calculate technical indicators
        processed_df = self.calculate_technical_indicators(self.raw_data)

        # Prepare features and labels
        features = self.prepare_features(processed_df)
        labels = self.create_labels(processed_df)

        # Remove rows with NaN values
        valid_indices = ~np.isnan(features).any(axis=1)
        features = features[valid_indices]
        labels = labels[valid_indices]
        processed_df = processed_df[valid_indices].reset_index(drop=True)

        # Split data
        split_idx = int(len(features) * train_split)

        train_features = features[:split_idx]
        test_features = features[split_idx:]
        train_labels = labels[:split_idx]
        test_labels = labels[split_idx:]

        # Normalize features
        train_features_scaled = self.scaler.fit_transform(train_features)
        test_features_scaled = self.scaler.transform(test_features)

        self.processed_data = processed_df

        return {
            'train_features': train_features_scaled,
            'test_features': test_features_scaled,
            'train_labels': train_labels,
            'test_labels': test_labels,
            'processed_df': processed_df,
            'split_idx': split_idx,
            'feature_names': [
                'open', 'high', 'low', 'close', 'volume',
                'sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10',
                'rsi', 'macd', 'macd_signal', 'price_change', 'volatility'
            ]
        }
