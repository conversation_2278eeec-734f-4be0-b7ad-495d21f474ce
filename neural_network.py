import numpy as np
from typing import List, Tuple

class NeuralNetwork:
    """
    Feedforward Neural Network for cryptocurrency trading signals.
    Architecture: Input(12) -> Hidden(16) -> Hidden(8) -> Output(3)
    """

    def __init__(self, input_size: int = 12, hidden_sizes: List[int] = [16, 8], output_size: int = 3):
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.output_size = output_size

        # Initialize network architecture
        self.layer_sizes = [input_size] + hidden_sizes + [output_size]
        self.num_layers = len(self.layer_sizes) - 1

        # Initialize weights and biases
        self.weights = []
        self.biases = []

        self._initialize_parameters()

    def _initialize_parameters(self):
        """Initialize weights and biases using Xavier initialization."""
        for i in range(self.num_layers):
            # Xavier initialization for weights
            weight_matrix = np.random.randn(self.layer_sizes[i], self.layer_sizes[i + 1]) * np.sqrt(2.0 / self.layer_sizes[i])
            self.weights.append(weight_matrix)

            # Initialize biases to zero
            bias_vector = np.zeros((1, self.layer_sizes[i + 1]))
            self.biases.append(bias_vector)

    def set_parameters(self, parameters: np.ndarray):
        """Set network parameters from a flat array (used by genetic algorithm)."""
        param_idx = 0

        # Set weights
        for i in range(self.num_layers):
            weight_size = self.layer_sizes[i] * self.layer_sizes[i + 1]
            weight_flat = parameters[param_idx:param_idx + weight_size]
            self.weights[i] = weight_flat.reshape(self.layer_sizes[i], self.layer_sizes[i + 1])
            param_idx += weight_size

        # Set biases
        for i in range(self.num_layers):
            bias_size = self.layer_sizes[i + 1]
            self.biases[i] = parameters[param_idx:param_idx + bias_size].reshape(1, -1)
            param_idx += bias_size

    def get_parameters(self) -> np.ndarray:
        """Get network parameters as a flat array (used by genetic algorithm)."""
        parameters = []

        # Flatten weights
        for weight_matrix in self.weights:
            parameters.extend(weight_matrix.flatten())

        # Flatten biases
        for bias_vector in self.biases:
            parameters.extend(bias_vector.flatten())

        return np.array(parameters)

    def get_parameter_count(self) -> int:
        """Get total number of parameters in the network."""
        count = 0

        # Count weights
        for i in range(self.num_layers):
            count += self.layer_sizes[i] * self.layer_sizes[i + 1]

        # Count biases
        for i in range(self.num_layers):
            count += self.layer_sizes[i + 1]

        return count

    def relu(self, x: np.ndarray) -> np.ndarray:
        """ReLU activation function."""
        return np.maximum(0, x)

    def softmax(self, x: np.ndarray) -> np.ndarray:
        """Softmax activation function."""
        # Subtract max for numerical stability
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)

    def forward(self, X: np.ndarray) -> np.ndarray:
        """Forward pass through the network."""
        if X.ndim == 1:
            X = X.reshape(1, -1)

        activation = X

        # Forward pass through hidden layers
        for i in range(self.num_layers - 1):
            z = np.dot(activation, self.weights[i]) + self.biases[i]
            activation = self.relu(z)

        # Output layer with softmax
        z_output = np.dot(activation, self.weights[-1]) + self.biases[-1]
        output = self.softmax(z_output)

        return output

    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions and return class indices."""
        probabilities = self.forward(X)
        return np.argmax(probabilities, axis=1)

    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Return prediction probabilities."""
        return self.forward(X)

    def copy(self):
        """Create a copy of the neural network."""
        new_nn = NeuralNetwork(self.input_size, self.hidden_sizes, self.output_size)
        new_nn.set_parameters(self.get_parameters())
        return new_nn


class NetworkFactory:
    """Factory class for creating neural networks with different architectures."""

    @staticmethod
    def create_trading_network() -> NeuralNetwork:
        """Create a neural network optimized for trading signals."""
        return NeuralNetwork(
            input_size=12,      # 10 technical indicators + 2 position state features
            hidden_sizes=[16, 8],  # Two hidden layers (slightly larger for more features)
            output_size=3       # Buy, Sell, Hold
        )

    @staticmethod
    def create_custom_network(input_size: int, hidden_sizes: List[int], output_size: int) -> NeuralNetwork:
        """Create a custom neural network architecture."""
        return NeuralNetwork(input_size, hidden_sizes, output_size)


def test_neural_network():
    """Test function for the neural network."""
    print("Testing Neural Network...")

    # Create a test network
    nn = NetworkFactory.create_trading_network()

    print(f"Network architecture: {nn.layer_sizes}")
    print(f"Total parameters: {nn.get_parameter_count()}")

    # Test with random input
    test_input = np.random.randn(10, 12)  # 10 samples, 12 features

    # Forward pass
    output = nn.forward(test_input)
    print(f"Output shape: {output.shape}")
    print(f"Output probabilities sum: {np.sum(output, axis=1)}")  # Should be close to 1

    # Test predictions
    predictions = nn.predict(test_input)
    print(f"Predictions: {predictions}")

    # Test parameter manipulation
    original_params = nn.get_parameters()
    print(f"Parameter vector length: {len(original_params)}")

    # Modify parameters and restore
    modified_params = original_params + np.random.randn(len(original_params)) * 0.1
    nn.set_parameters(modified_params)

    new_output = nn.forward(test_input)
    print(f"Output changed after parameter modification: {not np.allclose(output, new_output)}")

    print("Neural Network test completed successfully!")


if __name__ == "__main__":
    test_neural_network()
