#!/usr/bin/env python3
"""
Full analysis run of the cryptocurrency trading bot with comprehensive parameters.
"""

from main import CryptoTradingBot

def main():
    """Run the trading bot with full parameters for comprehensive analysis."""
    print("CRYPTOCURRENCY TRADING BOT - FULL ANALYSIS")
    print("=" * 70)
    
    # Full configuration for comprehensive analysis
    config = {
        'population_size': 300,      # Larger population for better diversity
        'generations': 100,          # More generations for better evolution
        'mutation_rate': 0.1,       # Standard mutation rate
        'crossover_rate': 0.8,      # High crossover rate
        'elite_percentage': 0.1,    # Keep top 10%
        'initial_capital': 10000.0,
        'transaction_fee': 0.001,   # 0.1% per trade (realistic)
        'train_split': 0.7,         # Use 70% for training, 30% for testing
        'save_results': True,
        'verbose': True
    }
    
    print("Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print()
    
    print("This analysis will:")
    print("- Load and process cryptocurrency OHLCV data")
    print("- Calculate technical indicators (RSI, MACD, Moving Averages, etc.)")
    print("- Evolve neural network parameters using genetic algorithm")
    print("- Test the best individual on unseen data")
    print("- Generate comprehensive visualizations")
    print("- Save all results and the best model")
    print()
    
    # Create and run the trading bot
    bot = CryptoTradingBot(config)
    results = bot.run_complete_analysis()
    
    if results:
        print("\n" + "=" * 70)
        print("FULL ANALYSIS COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        
        # Print key insights
        if results['test_results']:
            test_results = results['test_results']
            print("\nKEY INSIGHTS:")
            print(f"- Training Performance: {results['evolution_results']['generation_stats'][-1]['best_return']:.2f}% return")
            print(f"- Test Performance: {test_results['total_return_pct']:.2f}% return")
            print(f"- Risk-Adjusted Return (Sharpe): {test_results['sharpe_ratio']:.4f}")
            print(f"- Maximum Drawdown: {test_results['max_drawdown_pct']:.2f}%")
            print(f"- Trading Activity: {test_results['total_trades']} trades executed")
            print(f"- Win Rate: {test_results['win_rate_pct']:.1f}%")
        
        print("\nGenerated Files:")
        print("- results/evolution_progress_*.png - Shows fitness evolution over generations")
        print("- results/trading_results_*.png - Shows price chart with buy/sell signals")
        print("- results/technical_indicators_*.png - Shows technical analysis indicators")
        print("- results/summary_report_*.txt - Comprehensive performance report")
        print("- results/best_model_*.npy - Best neural network parameters")
        
        print("\nNEXT STEPS:")
        print("1. Review the generated charts to understand the trading strategy")
        print("2. Analyze the buy/sell signals on the price chart")
        print("3. Consider adjusting parameters for different market conditions")
        print("4. Use the saved model for live trading (with proper risk management)")
        
    else:
        print("\nAnalysis failed! Check error messages above.")

if __name__ == "__main__":
    main()
