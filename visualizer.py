import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

class TradingVisualizer:
    """
    Visualization tools for cryptocurrency trading bot results.
    """

    def __init__(self, figsize: Tuple[int, int] = (15, 10)):
        self.figsize = figsize
        plt.style.use('default')

    def plot_evolution_progress(self, evolution_results: Dict[str, Any], save_path: str = None):
        """Plot genetic algorithm evolution progress."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize)

        generations = range(len(evolution_results['best_fitness_history']))
        best_fitness = evolution_results['best_fitness_history']
        mean_fitness = evolution_results['mean_fitness_history']

        # Best and mean fitness over generations
        ax1.plot(generations, best_fitness, 'b-', linewidth=2, label='Best Fitness')
        ax1.plot(generations, mean_fitness, 'r--', linewidth=1, label='Mean Fitness')
        ax1.set_xlabel('Generation')
        ax1.set_ylabel('Fitness Score')
        ax1.set_title('Fitness Evolution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Best return over generations
        if 'generation_stats' in evolution_results:
            returns = [stat['best_return'] for stat in evolution_results['generation_stats']]
            ax2.plot(generations, returns, 'g-', linewidth=2)
            ax2.set_xlabel('Generation')
            ax2.set_ylabel('Best Return (%)')
            ax2.set_title('Best Return Evolution')
            ax2.grid(True, alpha=0.3)

            # Sharpe ratio evolution
            sharpe_ratios = [stat['best_sharpe'] for stat in evolution_results['generation_stats']]
            ax3.plot(generations, sharpe_ratios, 'm-', linewidth=2)
            ax3.set_xlabel('Generation')
            ax3.set_ylabel('Best Sharpe Ratio')
            ax3.set_title('Sharpe Ratio Evolution')
            ax3.grid(True, alpha=0.3)

            # Number of trades evolution
            trades = [stat['best_trades'] for stat in evolution_results['generation_stats']]
            ax4.plot(generations, trades, 'c-', linewidth=2)
            ax4.set_xlabel('Generation')
            ax4.set_ylabel('Number of Trades')
            ax4.set_title('Trading Frequency Evolution')
            ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Evolution progress plot saved to {save_path}")

        plt.show()

    def plot_trading_results(self, processed_df: pd.DataFrame, simulation_results: Dict[str, Any],
                           split_idx: int = None, save_path: str = None):
        """Plot comprehensive trading results with buy/sell signals."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize)

        # Prepare data
        timestamps = processed_df['datetime'].values
        prices = processed_df['close'].values

        # Extract buy and sell signals
        buy_signals = []
        sell_signals = []

        for signal_entry in simulation_results['signals_history']:
            if signal_entry['trade_executed']:
                timestamp_idx = None
                # Find the corresponding timestamp index
                for i, ts in enumerate(processed_df['unixTime']):
                    if ts == signal_entry['timestamp']:
                        timestamp_idx = i
                        break

                if timestamp_idx is not None:
                    if signal_entry['signal'] == 0:  # Buy
                        buy_signals.append((timestamps[timestamp_idx], signal_entry['price']))
                    elif signal_entry['signal'] == 1:  # Sell
                        sell_signals.append((timestamps[timestamp_idx], signal_entry['price']))

        # Plot 1: Price chart with buy/sell signals
        ax1.plot(timestamps, prices, 'k-', linewidth=1, label='Price', alpha=0.7)

        if buy_signals:
            buy_times, buy_prices = zip(*buy_signals)
            ax1.scatter(buy_times, buy_prices, color='green', marker='^', s=100,
                       label=f'Buy Signals ({len(buy_signals)})', zorder=5)

        if sell_signals:
            sell_times, sell_prices = zip(*sell_signals)
            ax1.scatter(sell_times, sell_prices, color='red', marker='v', s=100,
                       label=f'Sell Signals ({len(sell_signals)})', zorder=5)

        # Mark train/test split if provided
        if split_idx is not None and split_idx < len(timestamps):
            ax1.axvline(x=timestamps[split_idx], color='blue', linestyle='--',
                       alpha=0.7, label='Train/Test Split')

        ax1.set_xlabel('Time')
        ax1.set_ylabel('Price')
        ax1.set_title('Trading Signals on Price Chart')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Format x-axis for better readability
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax1.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        # Plot 2: Portfolio value over time
        portfolio_history = simulation_results['portfolio_history']
        portfolio_times = []
        portfolio_values = []

        for entry in portfolio_history:
            # Find corresponding timestamp
            for i, ts in enumerate(processed_df['unixTime']):
                if ts == entry['timestamp']:
                    portfolio_times.append(timestamps[i])
                    portfolio_values.append(entry['total_value'])
                    break

        if portfolio_times:
            ax2.plot(portfolio_times, portfolio_values, 'b-', linewidth=2, label='Portfolio Value')
            ax2.axhline(y=simulation_results['initial_capital'], color='gray',
                       linestyle='--', alpha=0.7, label='Initial Capital')

            ax2.set_xlabel('Time')
            ax2.set_ylabel('Portfolio Value ($)')
            ax2.set_title('Portfolio Performance')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # Format x-axis
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax2.xaxis.set_major_locator(mdates.HourLocator(interval=6))
            plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        # Plot 3: Returns distribution
        if len(portfolio_values) > 1:
            returns = np.diff(portfolio_values) / portfolio_values[:-1]
            ax3.hist(returns, bins=30, alpha=0.7, color='purple', edgecolor='black')
            ax3.axvline(x=np.mean(returns), color='red', linestyle='--',
                       label=f'Mean: {np.mean(returns):.4f}')
            ax3.set_xlabel('Returns')
            ax3.set_ylabel('Frequency')
            ax3.set_title('Returns Distribution')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # Plot 4: Performance metrics summary
        metrics = [
            f"Total Return: {simulation_results['total_return_pct']:.2f}%",
            f"Sharpe Ratio: {simulation_results['sharpe_ratio']:.4f}",
            f"Max Drawdown: {simulation_results['max_drawdown_pct']:.2f}%",
            f"Win Rate: {simulation_results['win_rate_pct']:.2f}%",
            f"Total Trades: {simulation_results['total_trades']}",
            f"Final Value: ${simulation_results['final_value']:.2f}",
            f"Total Fees: ${simulation_results['total_fees']:.2f}"
        ]

        ax4.text(0.1, 0.9, '\n'.join(metrics), transform=ax4.transAxes,
                fontsize=12, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        ax4.set_title('Performance Summary')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Trading results plot saved to {save_path}")

        plt.show()

    def plot_technical_indicators(self, processed_df: pd.DataFrame, start_idx: int = 0,
                                end_idx: int = None, save_path: str = None):
        """Plot technical indicators used in the analysis."""
        if end_idx is None:
            end_idx = len(processed_df)

        df_subset = processed_df.iloc[start_idx:end_idx].copy()

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=self.figsize)

        timestamps = df_subset['datetime'].values

        # Plot 1: Price with moving averages
        ax1.plot(timestamps, df_subset['close'], 'k-', linewidth=1, label='Close Price')
        ax1.plot(timestamps, df_subset['sma_5'], 'b-', linewidth=1, label='SMA 5')
        ax1.plot(timestamps, df_subset['sma_10'], 'r-', linewidth=1, label='SMA 10')
        ax1.plot(timestamps, df_subset['sma_20'], 'g-', linewidth=1, label='SMA 20')
        ax1.plot(timestamps, df_subset['ema_12'], 'm-', linewidth=1, label='EMA 12')
        ax1.plot(timestamps, df_subset['ema_26'], 'c-', linewidth=1, label='EMA 26')
        ax1.set_xlabel('Time')
        ax1.set_ylabel('Price')
        ax1.set_title('Price and Moving Averages')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: RSI
        ax2.plot(timestamps, df_subset['rsi'], 'purple', linewidth=2)
        ax2.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='Overbought (70)')
        ax2.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='Oversold (30)')
        ax2.set_xlabel('Time')
        ax2.set_ylabel('RSI')
        ax2.set_title('Relative Strength Index')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 100)

        # Plot 3: Awesome Oscillator
        ax3.plot(timestamps, df_subset['awesome_oscillator'], 'orange', linewidth=2, label='Awesome Oscillator')
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.set_xlabel('Time')
        ax3.set_ylabel('AO')
        ax3.set_title('Awesome Oscillator')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Plot 4: Bollinger Bands Position
        ax4.plot(timestamps, df_subset['bb_position'], 'purple', linewidth=2, label='BB Position')
        ax4.axhline(y=0.5, color='black', linestyle='-', alpha=0.3, label='Middle')
        ax4.axhline(y=0.8, color='red', linestyle='--', alpha=0.7, label='Overbought (0.8)')
        ax4.axhline(y=0.2, color='green', linestyle='--', alpha=0.7, label='Oversold (0.2)')
        ax4.set_xlabel('Time')
        ax4.set_ylabel('BB Position')
        ax4.set_title('Bollinger Bands Position')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_ylim(0, 1)

        # Format x-axes
        for ax in [ax1, ax2, ax3, ax4]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Technical indicators plot saved to {save_path}")

        plt.show()

    def create_summary_report(self, evolution_results: Dict[str, Any],
                            simulation_results: Dict[str, Any], save_path: str = None):
        """Create a comprehensive summary report."""
        report = []
        report.append("=" * 80)
        report.append("CRYPTOCURRENCY TRADING BOT - GENETIC ALGORITHM RESULTS")
        report.append("=" * 80)
        report.append("")

        # Evolution Summary
        report.append("GENETIC ALGORITHM EVOLUTION SUMMARY:")
        report.append("-" * 40)
        if 'generation_stats' in evolution_results:
            final_gen = evolution_results['generation_stats'][-1]
            report.append(f"Total Generations: {len(evolution_results['generation_stats'])}")
            report.append(f"Final Best Fitness: {final_gen['best_fitness']:.4f}")
            report.append(f"Final Mean Fitness: {final_gen['mean_fitness']:.4f}")
            report.append(f"Best Return Achieved: {final_gen['best_return']:.2f}%")
            report.append(f"Best Sharpe Ratio: {final_gen['best_sharpe']:.4f}")
        report.append("")

        # Trading Performance Summary
        report.append("TRADING PERFORMANCE SUMMARY:")
        report.append("-" * 40)
        report.append(f"Initial Capital: ${simulation_results['initial_capital']:,.2f}")
        report.append(f"Final Portfolio Value: ${simulation_results['final_value']:,.2f}")
        report.append(f"Total Return: {simulation_results['total_return_pct']:.2f}%")
        report.append(f"Sharpe Ratio: {simulation_results['sharpe_ratio']:.4f}")
        report.append(f"Maximum Drawdown: {simulation_results['max_drawdown_pct']:.2f}%")
        report.append(f"Win Rate: {simulation_results['win_rate_pct']:.2f}%")
        report.append(f"Total Trades Executed: {simulation_results['total_trades']}")
        report.append(f"Winning Trades: {simulation_results['winning_trades']}")
        report.append(f"Losing Trades: {simulation_results['losing_trades']}")
        report.append(f"Total Fees Paid: ${simulation_results['total_fees']:.2f}")
        report.append("")

        # Risk Metrics
        if 'excess_return' in simulation_results:
            report.append("RISK ANALYSIS:")
            report.append("-" * 40)
            report.append(f"Benchmark Return: {simulation_results.get('benchmark_return', 0) * 100:.2f}%")
            report.append(f"Excess Return: {simulation_results['excess_return'] * 100:.2f}%")

        report_text = "\n".join(report)
        print(report_text)

        if save_path:
            with open(save_path, 'w') as f:
                f.write(report_text)
            print(f"\nSummary report saved to {save_path}")

        return report_text


def test_visualizer():
    """Test function for the visualizer."""
    print("Testing Visualizer...")

    # Create sample data for testing
    dates = pd.date_range('2023-01-01', periods=100, freq='H')
    sample_df = pd.DataFrame({
        'datetime': dates,
        'unixTime': [int(d.timestamp()) for d in dates],
        'close': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'sma_5': 100 + np.cumsum(np.random.randn(100) * 0.3),
        'sma_10': 100 + np.cumsum(np.random.randn(100) * 0.2),
        'sma_20': 100 + np.cumsum(np.random.randn(100) * 0.1),
        'ema_12': 100 + np.cumsum(np.random.randn(100) * 0.25),
        'ema_26': 100 + np.cumsum(np.random.randn(100) * 0.15),
        'rsi': np.random.uniform(20, 80, 100),
        'macd': np.random.randn(100) * 0.5,
        'macd_signal': np.random.randn(100) * 0.3,
        'awesome_oscillator': np.random.randn(100) * 0.2,
        'bb_position': np.random.uniform(0, 1, 100)
    })

    # Create sample simulation results
    sample_simulation = {
        'initial_capital': 10000,
        'final_value': 11500,
        'total_return_pct': 15.0,
        'sharpe_ratio': 1.2,
        'max_drawdown_pct': 5.0,
        'win_rate_pct': 65.0,
        'total_trades': 20,
        'winning_trades': 13,
        'losing_trades': 7,
        'total_fees': 50.0,
        'signals_history': [
            {'timestamp': int(dates[10].timestamp()), 'signal': 0, 'price': 105, 'trade_executed': True},
            {'timestamp': int(dates[30].timestamp()), 'signal': 1, 'price': 110, 'trade_executed': True},
            {'timestamp': int(dates[50].timestamp()), 'signal': 0, 'price': 108, 'trade_executed': True},
            {'timestamp': int(dates[80].timestamp()), 'signal': 1, 'price': 115, 'trade_executed': True}
        ],
        'portfolio_history': [
            {'timestamp': int(d.timestamp()), 'total_value': 10000 + i * 15}
            for i, d in enumerate(dates)
        ]
    }

    # Create sample evolution results
    sample_evolution = {
        'best_fitness_history': [0.1 + i * 0.05 + np.random.randn() * 0.02 for i in range(20)],
        'mean_fitness_history': [0.05 + i * 0.03 + np.random.randn() * 0.01 for i in range(20)],
        'generation_stats': [
            {
                'best_return': 5 + i * 0.5 + np.random.randn() * 0.5,
                'best_sharpe': 0.5 + i * 0.03 + np.random.randn() * 0.05,
                'best_trades': 10 + np.random.randint(-2, 3)
            } for i in range(20)
        ]
    }

    # Test visualizer
    visualizer = TradingVisualizer()

    print("Testing technical indicators plot...")
    visualizer.plot_technical_indicators(sample_df, start_idx=0, end_idx=50)

    print("Testing evolution progress plot...")
    visualizer.plot_evolution_progress(sample_evolution)

    print("Testing trading results plot...")
    visualizer.plot_trading_results(sample_df, sample_simulation, split_idx=70)

    print("Testing summary report...")
    visualizer.create_summary_report(sample_evolution, sample_simulation)

    print("Visualizer test completed successfully!")


if __name__ == "__main__":
    test_visualizer()
