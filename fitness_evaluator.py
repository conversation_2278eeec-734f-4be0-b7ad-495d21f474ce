import numpy as np
from typing import Dict, Any
from neural_network import NeuralNetwork
from trading_simulator import TradingSimulator

class FitnessEvaluator:
    """
    Evaluates the fitness of neural networks for cryptocurrency trading.
    Combines multiple performance metrics into a single fitness score.
    """
    
    def __init__(self, features: np.ndarray, prices: np.ndarray, timestamps: list,
                 initial_capital: float = 10000.0, transaction_fee: float = 0.001):
        self.features = features
        self.prices = prices
        self.timestamps = timestamps
        self.initial_capital = initial_capital
        self.transaction_fee = transaction_fee
        
        # Create trading simulator
        self.simulator = TradingSimulator(initial_capital, transaction_fee)
        
        # Fitness weights for different metrics
        self.weights = {
            'return': 0.4,          # Total return weight
            'sharpe': 0.3,          # Sharpe ratio weight
            'drawdown': 0.2,        # Max drawdown weight (penalty)
            'trades': 0.1           # Number of trades weight (penalty for overtrading)
        }
        
        # Benchmark metrics for normalization
        self.benchmark_return = 0.0  # Buy and hold return
        self.calculate_benchmark()
    
    def calculate_benchmark(self):
        """Calculate buy-and-hold benchmark performance."""
        if len(self.prices) > 0:
            self.benchmark_return = (self.prices[-1] - self.prices[0]) / self.prices[0]
    
    def evaluate_individual(self, neural_network: NeuralNetwork) -> Dict[str, Any]:
        """
        Evaluate a single neural network individual.
        
        Args:
            neural_network: Neural network to evaluate
            
        Returns:
            Dict containing fitness score and detailed metrics
        """
        try:
            # Run trading simulation
            results = self.simulator.simulate_trading(
                neural_network, self.features, self.prices, self.timestamps
            )
            
            if 'error' in results:
                return {'fitness': -1000.0, 'error': results['error']}
            
            # Extract key metrics
            total_return = results['total_return']
            sharpe_ratio = results['sharpe_ratio']
            max_drawdown = results['max_drawdown']
            total_trades = results['total_trades']
            
            # Calculate fitness components
            fitness_components = self.calculate_fitness_components(
                total_return, sharpe_ratio, max_drawdown, total_trades
            )
            
            # Calculate overall fitness
            fitness = self.calculate_overall_fitness(fitness_components)
            
            return {
                'fitness': fitness,
                'total_return': total_return,
                'total_return_pct': total_return * 100,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'max_drawdown_pct': max_drawdown * 100,
                'total_trades': total_trades,
                'win_rate': results['win_rate'],
                'win_rate_pct': results['win_rate_pct'],
                'final_value': results['final_value'],
                'total_fees': results['total_fees'],
                'fitness_components': fitness_components,
                'benchmark_return': self.benchmark_return,
                'excess_return': total_return - self.benchmark_return
            }
            
        except Exception as e:
            return {'fitness': -1000.0, 'error': str(e)}
    
    def calculate_fitness_components(self, total_return: float, sharpe_ratio: float,
                                   max_drawdown: float, total_trades: int) -> Dict[str, float]:
        """Calculate individual fitness components."""
        
        # Return component (normalized and capped)
        return_score = min(max(total_return * 10, -5), 5)  # Cap between -5 and 5
        
        # Sharpe ratio component (normalized)
        sharpe_score = min(max(sharpe_ratio, -2), 2)  # Cap between -2 and 2
        
        # Drawdown penalty (negative score for high drawdown)
        drawdown_penalty = -max_drawdown * 10  # Penalty increases with drawdown
        
        # Trading frequency penalty (discourage overtrading)
        expected_trades = len(self.features) * 0.1  # Expect ~10% of periods to have trades
        trade_penalty = -abs(total_trades - expected_trades) / expected_trades
        
        return {
            'return_score': return_score,
            'sharpe_score': sharpe_score,
            'drawdown_penalty': drawdown_penalty,
            'trade_penalty': trade_penalty
        }
    
    def calculate_overall_fitness(self, components: Dict[str, float]) -> float:
        """Calculate weighted overall fitness score."""
        fitness = (
            self.weights['return'] * components['return_score'] +
            self.weights['sharpe'] * components['sharpe_score'] +
            self.weights['drawdown'] * components['drawdown_penalty'] +
            self.weights['trades'] * components['trade_penalty']
        )
        
        return fitness
    
    def evaluate_population(self, population: list) -> list:
        """
        Evaluate an entire population of neural networks.
        
        Args:
            population: List of neural networks
            
        Returns:
            List of fitness evaluations
        """
        evaluations = []
        
        for i, individual in enumerate(population):
            evaluation = self.evaluate_individual(individual)
            evaluation['individual_id'] = i
            evaluations.append(evaluation)
        
        return evaluations
    
    def get_fitness_statistics(self, evaluations: list) -> Dict[str, float]:
        """Calculate statistics for a population's fitness scores."""
        fitness_scores = [eval_result['fitness'] for eval_result in evaluations 
                         if 'error' not in eval_result]
        
        if not fitness_scores:
            return {'error': 'No valid fitness scores'}
        
        return {
            'mean_fitness': np.mean(fitness_scores),
            'std_fitness': np.std(fitness_scores),
            'min_fitness': np.min(fitness_scores),
            'max_fitness': np.max(fitness_scores),
            'median_fitness': np.median(fitness_scores)
        }
    
    def get_best_individual(self, evaluations: list) -> Dict[str, Any]:
        """Find the best individual from evaluations."""
        valid_evaluations = [eval_result for eval_result in evaluations 
                           if 'error' not in eval_result]
        
        if not valid_evaluations:
            return {'error': 'No valid evaluations'}
        
        best_evaluation = max(valid_evaluations, key=lambda x: x['fitness'])
        return best_evaluation
    
    def update_weights(self, return_weight: float = None, sharpe_weight: float = None,
                      drawdown_weight: float = None, trades_weight: float = None):
        """Update fitness component weights."""
        if return_weight is not None:
            self.weights['return'] = return_weight
        if sharpe_weight is not None:
            self.weights['sharpe'] = sharpe_weight
        if drawdown_weight is not None:
            self.weights['drawdown'] = drawdown_weight
        if trades_weight is not None:
            self.weights['trades'] = trades_weight
        
        # Normalize weights to sum to 1
        total_weight = sum(self.weights.values())
        for key in self.weights:
            self.weights[key] /= total_weight


def test_fitness_evaluator():
    """Test function for the fitness evaluator."""
    print("Testing Fitness Evaluator...")
    
    from neural_network import NetworkFactory
    
    # Create test data
    n_samples = 100
    features = np.random.randn(n_samples, 15)
    prices = 100 + np.cumsum(np.random.randn(n_samples) * 0.01)
    timestamps = list(range(n_samples))
    
    # Create fitness evaluator
    evaluator = FitnessEvaluator(features, prices, timestamps)
    
    # Create test population
    population = [NetworkFactory.create_trading_network() for _ in range(5)]
    
    # Evaluate population
    evaluations = evaluator.evaluate_population(population)
    
    print(f"Evaluated {len(evaluations)} individuals")
    
    # Print results
    for i, eval_result in enumerate(evaluations):
        if 'error' not in eval_result:
            print(f"Individual {i}: Fitness={eval_result['fitness']:.4f}, "
                  f"Return={eval_result['total_return_pct']:.2f}%, "
                  f"Sharpe={eval_result['sharpe_ratio']:.4f}")
        else:
            print(f"Individual {i}: Error - {eval_result['error']}")
    
    # Get statistics
    stats = evaluator.get_fitness_statistics(evaluations)
    print(f"\nPopulation Statistics:")
    for key, value in stats.items():
        print(f"{key}: {value:.4f}")
    
    # Get best individual
    best = evaluator.get_best_individual(evaluations)
    if 'error' not in best:
        print(f"\nBest Individual:")
        print(f"Fitness: {best['fitness']:.4f}")
        print(f"Return: {best['total_return_pct']:.2f}%")
        print(f"Sharpe Ratio: {best['sharpe_ratio']:.4f}")
    
    print("Fitness Evaluator test completed successfully!")


if __name__ == "__main__":
    test_fitness_evaluator()
